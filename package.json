{"name": "uecc_classic", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@rails/actioncable": "^8.0.201", "@tiptap/extension-code-block-lowlight": "^3.2.0", "@tiptap/extension-collaboration": "^3.2.0", "@tiptap/extension-collaboration-caret": "^3.2.0", "@tiptap/extension-file-handler": "^3.2.0", "@tiptap/extension-image": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "lowlight": "^3.3.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "sass": "^1.90.0", "y-protocols": "^1.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4"}}