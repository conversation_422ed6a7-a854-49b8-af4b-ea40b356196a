# 在线拼图工具

一个功能强大的纯前端拼图工具，支持布局拼图和长图拼接两大核心模式，以及丰富的创意对象功能。

## 🚀 功能特性

### 核心模式

#### 1. 布局拼图模式
- **预设布局选择**：提供1-9张图片的多种专业布局
  - 1张图片：居中布局
  - 2张图片：水平、垂直、重叠布局
  - 3张图片：水平三联、垂直三联、三角布局、主次布局
  - 4张图片：四宫格、水平四联、主图三小布局
  - 5张图片：十字布局、主图四角布局
  - 6张图片：六宫格、双行布局
  - 9张图片：九宫格、中心焦点布局
- 自由拖拽、缩放、旋转图片
- 智能自动排列功能
- 支持多图片灵活布局
- 实时预览效果
- 一键重置布局功能

#### 2. 长图拼接模式
- 水平拼接：将多张图片水平排列成长图
- 垂直拼接：将多张图片垂直排列成长图
- 无缝拼接，无间距处理
- 适合制作时间线、流程图等

### 创意对象功能

#### 文字工具
- 添加自定义文字
- 调整字体大小（8-72px）
- 自定义文字颜色
- 支持拖拽移动

#### 图形工具
- **箭头**：可调整颜色和粗细
- **矩形**：可设置边框和填充颜色
- **圆形**：可设置边框和填充颜色
- 所有图形支持拖拽和调整大小

### 交互功能

#### 选择和编辑
- 点击选择对象
- 拖拽移动位置
- 拖拽角落调整大小
- 双击编辑属性
- Delete键删除对象

#### 图片管理
- 支持多图片同时上传
- 拖拽上传或点击选择
- 支持 PNG、JPG、GIF 格式
- 实时预览和编辑

#### 导出功能
- 一键导出为PNG图片
- 自动计算最佳画布尺寸
- 高质量图片输出
- 本地下载，无需上传

## 🎯 使用场景

- **教育培训**：制作教学图片、流程图
- **工作汇报**：拼接截图、制作对比图
- **社交分享**：制作九宫格、长图分享
- **设计原型**：快速布局设计稿
- **文档制作**：图片排版、说明图制作

## 🛠 技术特点

- **纯前端实现**：所有操作在本地完成，保护隐私
- **响应式设计**：适配不同屏幕尺寸
- **实时渲染**：即时预览编辑效果
- **高性能**：优化的Canvas渲染
- **无依赖**：不需要外部服务

## 📱 操作指南

### 基础操作
1. **上传图片**：拖拽或点击上传区域选择图片
2. **选择模式**：在"布局拼图"和"长图拼接"间切换
3. **选择布局**：在布局拼图模式下，从预设布局中选择合适的排列方式
4. **选择工具**：从工具栏选择需要的工具
5. **编辑对象**：点击选择，拖拽移动，拖拽角落调整大小
6. **导出结果**：点击"导出图片"保存作品

### 快捷键
- `Delete` / `Backspace`：删除选中对象
- 鼠标拖拽：移动对象
- 拖拽角落：调整大小

### 工具说明
- **选择工具**：用于选择和移动对象
- **文字工具**：点击画布添加文字
- **箭头工具**：点击画布添加箭头
- **方框工具**：点击画布添加矩形
- **圆圈工具**：点击画布添加圆形

## 🎨 设计理念

这个拼图工具的设计理念是"简单易用，功能强大"：

- **直观操作**：所见即所得的编辑体验
- **灵活布局**：支持自由创作和规范拼接
- **丰富功能**：满足各种拼图需求
- **隐私保护**：本地处理，数据不上传

## 🔧 开发信息

- 基于 React 18 + Next.js 15
- 使用 Tailwind CSS 样式
- 纯前端实现，无后端依赖
- 响应式设计，支持移动端

## 📝 更新日志

### v1.1.0 (2025-01-11)
- ✨ 新增预设布局选择功能
- ✅ 支持1-9张图片的多种专业布局
- ✅ 布局预览和选择界面
- ✅ 一键重置布局功能
- ✅ 优化用户体验和界面设计

### v1.0.0 (2025-01-11)
- ✨ 初始版本发布
- ✅ 布局拼图模式
- ✅ 长图拼接模式
- ✅ 创意对象功能
- ✅ 图片上传和管理
- ✅ 导出功能
- ✅ 响应式界面

---

**访问地址**：`/puzzle`

**技术支持**：如有问题或建议，欢迎反馈！
