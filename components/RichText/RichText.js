'use client'

import React from 'react'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'
import { EditorContent, useEditor } from '@tiptap/react'
import Collaboration from '@tiptap/extension-collaboration'
import StarterKit from '@tiptap/starter-kit'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import Image from '@tiptap/extension-image'
import <PERSON>Handler from '@tiptap/extension-file-handler'
import { all, createLowlight } from 'lowlight'

import * as Y from 'yjs'
import { createConsumer } from '@rails/actioncable'
import './styles.scss'
import CollaborationCaret from '@tiptap/extension-collaboration-caret'
import { Awareness } from "y-protocols/awareness"
import { encodeAwarenessUpdate, applyAwarenessUpdate } from "y-protocols/awareness"

// 随机生成用户名
function randomName() {
  const adjectives = [
    'Brave', 'Calm', 'Eager', 'Fancy', 'Gen<PERSON>', '<PERSON>', 'Jolly', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>ud', '<PERSON>lly', 'Witty', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Daring', 'Fuzzy', 'Giddy', 'Nimble'
  ]
  const animals = [
    'Lion', 'Tiger', 'Bear', 'Wolf', 'Fox', 'Eagle', 'Hawk', 'Shark', 'Otter', 'Panda',
    'Koala', 'Rabbit', 'Falcon', 'Moose', 'Swan', 'Dolphin', 'Penguin', 'Lynx', 'Bison', 'Seal'
  ]
  const adj = adjectives[Math.floor(Math.random() * adjectives.length)]
  const animal = animals[Math.floor(Math.random() * animals.length)]
  return `${adj} ${animal}`
}

// 随机生成颜色
function randomColor() {
  // 一组适合做用户高亮的柔和色
  const colors = [
    '#f783ac', '#70cff8', '#b9f18d', '#fbbc88', '#faf594', '#a3a1fb', '#ffb4a2', '#b5ead7',
    '#ffdac1', '#c7ceea', '#f6c6ea', '#c2f9bb', '#f7d6e0', '#e2f0cb', '#b5ead7', '#ffb7b2'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// 随机生成头像
function randomAvatar(name) {
  // 使用 unavatar.io 的自定义文本头像
  return `https://unavatar.io/initials/${encodeURIComponent(name)}`
}

export default function RichText() {
  const doc = new Y.Doc()
  // 建立 Action Cable 连接
  const cable = createConsumer(`${process.env.NEXT_PUBLIC_WEBSOCKET_URL}`)
  const lowlight = createLowlight(all)
  const awareness = new Awareness(doc)

  // 生成本地用户信息
  const [localUser] = React.useState(() => {
    const name = randomName()
    const color = randomColor()
    const avatar = randomAvatar(name)
    return { name, color, avatar }
  })

  // 订阅某个文档房间
  const [subscription, setSubscription] = React.useState(null);

  // 用 ref 存储 doc.on("update") 的回调，避免多次绑定
  const subscriptionRef = React.useRef(null);

  React.useEffect(() => {
    const sub = cable.subscriptions.create(
      { channel: "YjsChannel", room: "uecc_online_text" },
      {
        received(data) {
          if (data.type === "awareness") {
            applyAwarenessUpdate(awareness, new Uint8Array(data.update), subscription)
          } else {
            // 收到 update
            const update = Uint8Array.from(atob(data.update), c => c.charCodeAt(0))
            Y.applyUpdate(doc, update)
          }
        }
      }
    );
    setSubscription(sub);
    subscriptionRef.current = sub;

    return () => {
      if (sub) {
        cable.subscriptions.remove(sub);
      }
      subscriptionRef.current = null;
    };
  }, []);

  React.useEffect(() => {
    const handleAwarenessUpdate = ({ added, updated, removed }) => {
      const update = encodeAwarenessUpdate(awareness, added.concat(updated).concat(removed))
      if (subscriptionRef.current) {
        subscriptionRef.current.send({ type: "awareness", update: Array.from(update) })
      }
    }
    awareness.on("update", handleAwarenessUpdate)

    return () => {
      awareness.off("update", handleAwarenessUpdate)
    }
  }, [])

  React.useEffect(() => {
    // 定义回调
    const handleUpdate = update => {
      const snapshot = Y.encodeStateAsUpdate(doc)
      const snapshot_base64 = btoa(String.fromCharCode(...snapshot))
      // 只有 subscription 存在时才发送
      if (subscriptionRef.current) {
        subscriptionRef.current.send({ type: "text", update: snapshot_base64 })
      }
    };
    doc.on("update", handleUpdate);

    return () => {
      doc.off("update", handleUpdate);
    };
  }, []);

  // 封装上传图片的 API
  async function uploadImage(file) {
    const formData = new FormData();
    formData.append('file', file);

    try {
      // 假设后端上传接口为 /api/upload，返回 { url: "..." }
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/upload`, {
        method: 'POST',
        body: formData,
      });
      if (!response.ok) throw new Error('Upload failed');
      const data = await response.json();
      return data.url;
    } catch (err) {
      console.error('Image upload failed:', err);
      return null;
    }
  }

  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit,
      Document,
      Paragraph,
      Text,
      CodeBlockLowlight.configure({
        lowlight,
      }),
      Collaboration.configure({
        document: doc, // Configure Y.Doc for collaboration
      }),
      CollaborationCaret.configure({
        provider: { awareness },
        user: {
          name: localUser.name,
          color: localUser.color,
          avatar: localUser.avatar,
        },
      }),
      Image,
      FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
        onDrop: async (currentEditor, files, pos) => {
          for (const file of files) {
            const url = await uploadImage(file);
            if (url) {
              currentEditor
                .chain()
                .insertContentAt(pos, {
                  type: 'image',
                  attrs: {
                    src: url,
                  },
                })
                .focus()
                .run();
            }
          }
        },
        onPaste: async (currentEditor, files, htmlContent) => {
          for (const file of files) {
            if (htmlContent) {
              // 如果有 htmlContent，交给其他扩展处理
              console.log(htmlContent); // eslint-disable-line no-console
              return false;
            }
            const url = await uploadImage(file);
            if (url) {
              currentEditor
                .chain()
                .insertContentAt(currentEditor.state.selection.anchor, {
                  type: 'image',
                  attrs: {
                    src: url,
                  },
                })
                .focus()
                .run();
            }
          }
        },
      }),
    ],
    content: ``,
  })

  // 在 editor 初始化后，更新本地用户信息
  React.useEffect(() => {
    if (editor && localUser) {
      editor.commands.updateUser({
        name: localUser.name,
        color: localUser.color,
        avatar: localUser.avatar,
      })
    }
  }, [editor, localUser])

  return (
    <div>
      <button
        onClick={() => editor.chain().focus().toggleCodeBlock().run()}
        className={editor?.isActive('codeBlock') ? 'is-active' : ''}
      >
        Toggle code block
      </button>
      <EditorContent editor={editor} />
    </div>
  )
}