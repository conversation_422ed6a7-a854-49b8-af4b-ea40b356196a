'use client';

import { useState, useRef, useCallback } from 'react';
import ImageUploader from './puzzle/ImageUploader';
import Canvas from './puzzle/Canvas';
import Toolbar from './puzzle/Toolbar';
import CreativeObjects from './puzzle/CreativeObjects';
import LayoutSelector from './puzzle/LayoutSelector';

const PuzzleTool = () => {
  const [mode, setMode] = useState('layout'); // 'layout' or 'stitch'
  const [images, setImages] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [canvasObjects, setCanvasObjects] = useState([]);
  const [selectedObject, setSelectedObject] = useState(null);
  const [tool, setTool] = useState('select'); // 'select', 'text', 'arrow', 'rectangle', 'circle'
  const canvasRef = useRef(null);

  // 添加图片
  const handleImagesUpload = useCallback((newImages) => {
    const imageObjects = newImages.map((img, index) => ({
      id: Date.now() + index,
      type: 'image',
      src: img.src,
      x: 50 + index * 20,
      y: 50 + index * 20,
      width: img.width,
      height: img.height,
      rotation: 0,
      scale: 1,
      originalWidth: img.width,
      originalHeight: img.height,
    }));
    setImages(prev => [...prev, ...imageObjects]);
  }, []);

  // 删除图片
  const handleDeleteImage = useCallback((id) => {
    setImages(prev => prev.filter(img => img.id !== id));
    if (selectedImage?.id === id) {
      setSelectedImage(null);
    }
  }, [selectedImage]);

  // 更新图片属性
  const handleUpdateImage = useCallback((id, updates) => {
    setImages(prev => prev.map(img =>
      img.id === id ? { ...img, ...updates } : img
    ));
    if (selectedImage?.id === id) {
      setSelectedImage(prev => ({ ...prev, ...updates }));
    }
  }, [selectedImage]);

  // 添加创意对象
  const handleAddObject = useCallback((objectData) => {
    const newObject = {
      id: Date.now(),
      ...objectData,
    };
    setCanvasObjects(prev => [...prev, newObject]);
  }, []);

  // 更新创意对象
  const handleUpdateObject = useCallback((id, updates) => {
    setCanvasObjects(prev => prev.map(obj =>
      obj.id === id ? { ...obj, ...updates } : obj
    ));
    if (selectedObject?.id === id) {
      setSelectedObject(prev => ({ ...prev, ...updates }));
    }
  }, [selectedObject]);

  // 删除创意对象
  const handleDeleteObject = useCallback((id) => {
    setCanvasObjects(prev => prev.filter(obj => obj.id !== id));
    if (selectedObject?.id === id) {
      setSelectedObject(null);
    }
  }, [selectedObject]);

  // 应用预设布局
  const handleApplyLayout = useCallback((layout) => {
    if (images.length === 0 || !layout.positions) return;

    // 画布尺寸
    const canvasWidth = 2000;
    const canvasHeight = 1500;
    const padding = 100;
    const availableWidth = canvasWidth - padding * 2;
    const availableHeight = canvasHeight - padding * 2;

    const updatedImages = images.map((img, index) => {
      if (index >= layout.positions.length) return img;

      const pos = layout.positions[index];

      // 计算实际位置和尺寸
      const targetX = padding + pos.x * availableWidth;
      const targetY = padding + pos.y * availableHeight;
      const targetWidth = pos.width * availableWidth;
      const targetHeight = pos.height * availableHeight;

      // 计算缩放比例以适应目标尺寸
      const scaleX = targetWidth / img.originalWidth;
      const scaleY = targetHeight / img.originalHeight;
      const scale = Math.min(scaleX, scaleY);

      return {
        ...img,
        x: targetX,
        y: targetY,
        scale,
        width: img.originalWidth * scale,
        height: img.originalHeight * scale,
      };
    });

    setImages(updatedImages);
  }, [images]);

  // 布局拼图模式 - 自动排列
  const handleAutoLayout = useCallback(() => {
    if (images.length === 0) return;

    const cols = Math.ceil(Math.sqrt(images.length));
    const spacing = 20;
    const maxWidth = 200;
    const maxHeight = 200;

    const updatedImages = images.map((img, index) => {
      const row = Math.floor(index / cols);
      const col = index % cols;

      // 计算缩放比例以适应最大尺寸
      const scaleX = maxWidth / img.originalWidth;
      const scaleY = maxHeight / img.originalHeight;
      const scale = Math.min(scaleX, scaleY, 1);

      return {
        ...img,
        x: col * (maxWidth + spacing) + 50,
        y: row * (maxHeight + spacing) + 50,
        scale,
        width: img.originalWidth * scale,
        height: img.originalHeight * scale,
      };
    });

    setImages(updatedImages);
  }, [images]);

  // 长图拼接模式 - 水平排列
  const handleHorizontalStitch = useCallback(() => {
    if (images.length === 0) return;

    let currentX = 50;
    const y = 50;
    const spacing = 0; // 拼接模式无间距

    const updatedImages = images.map((img) => {
      const newImg = {
        ...img,
        x: currentX,
        y,
        scale: 1,
        width: img.originalWidth,
        height: img.originalHeight,
      };
      currentX += img.originalWidth + spacing;
      return newImg;
    });

    setImages(updatedImages);
  }, [images]);

  // 长图拼接模式 - 垂直排列
  const handleVerticalStitch = useCallback(() => {
    if (images.length === 0) return;

    const x = 50;
    let currentY = 50;
    const spacing = 0; // 拼接模式无间距

    const updatedImages = images.map((img) => {
      const newImg = {
        ...img,
        x,
        y: currentY,
        scale: 1,
        width: img.originalWidth,
        height: img.originalHeight,
      };
      currentY += img.originalHeight + spacing;
      return newImg;
    });

    setImages(updatedImages);
  }, [images]);

  // 导出功能
  const handleExport = useCallback(() => {
    if (!canvasRef.current) return;

    // 创建一个新的canvas用于导出
    const exportCanvas = document.createElement('canvas');
    const ctx = exportCanvas.getContext('2d');

    // 计算画布尺寸
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    [...images, ...canvasObjects].forEach(obj => {
      const left = obj.x;
      const top = obj.y;
      const right = obj.x + (obj.width || 100);
      const bottom = obj.y + (obj.height || 100);

      minX = Math.min(minX, left);
      minY = Math.min(minY, top);
      maxX = Math.max(maxX, right);
      maxY = Math.max(maxY, bottom);
    });

    const padding = 50;
    const canvasWidth = maxX - minX + padding * 2;
    const canvasHeight = maxY - minY + padding * 2;

    exportCanvas.width = canvasWidth;
    exportCanvas.height = canvasHeight;

    // 设置白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 导出图片和对象（这里需要实现具体的绘制逻辑）
    // 由于这是一个简化版本，实际实现需要更复杂的canvas绘制逻辑

    // 下载图片
    exportCanvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `puzzle-${Date.now()}.png`;
      a.click();
      URL.revokeObjectURL(url);
    });
  }, [images, canvasObjects]);

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* 头部工具栏 */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-800">在线拼图工具</h1>

            {/* 模式切换 */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setMode('layout')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  mode === 'layout'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                布局拼图
              </button>
              <button
                onClick={() => setMode('stitch')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  mode === 'stitch'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                长图拼接
              </button>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-2">
            {mode === 'layout' && (
              <>
                <button
                  onClick={handleAutoLayout}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  自动排列
                </button>
                <button
                  onClick={() => {
                    // 重置所有图片到初始位置
                    const resetImages = images.map((img, index) => ({
                      ...img,
                      x: 50 + index * 20,
                      y: 50 + index * 20,
                      scale: 1,
                      width: img.originalWidth,
                      height: img.originalHeight,
                      rotation: 0,
                    }));
                    setImages(resetImages);
                  }}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  disabled={images.length === 0}
                >
                  重置布局
                </button>
              </>
            )}
            {mode === 'stitch' && (
              <>
                <button
                  onClick={handleHorizontalStitch}
                  className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  水平拼接
                </button>
                <button
                  onClick={handleVerticalStitch}
                  className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                >
                  垂直拼接
                </button>
              </>
            )}
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
              disabled={images.length === 0}
            >
              导出图片
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* 左侧面板 */}
        <div className="w-80 bg-white border-r flex flex-col">
          {/* 图片上传区域 */}
          <div className="p-4 border-b">
            <ImageUploader onImagesUpload={handleImagesUpload} />
          </div>

          {/* 布局选择器 */}
          {mode === 'layout' && (
            <div className="p-4 border-b">
              <LayoutSelector
                images={images}
                onApplyLayout={handleApplyLayout}
                mode={mode}
              />
            </div>
          )}

          {/* 工具栏 */}
          <div className="p-4 border-b">
            <Toolbar
              tool={tool}
              onToolChange={setTool}
              mode={mode}
            />
          </div>

          {/* 创意对象控制 */}
          <div className="flex-1 p-4">
            <CreativeObjects
              tool={tool}
              onAddObject={handleAddObject}
              selectedObject={selectedObject}
              onUpdateObject={handleUpdateObject}
              onDeleteObject={handleDeleteObject}
            />
          </div>
        </div>

        {/* 主画布区域 */}
        <div className="flex-1 overflow-auto">
          <Canvas
            ref={canvasRef}
            mode={mode}
            images={images}
            canvasObjects={canvasObjects}
            selectedImage={selectedImage}
            selectedObject={selectedObject}
            tool={tool}
            onSelectImage={setSelectedImage}
            onSelectObject={setSelectedObject}
            onUpdateImage={handleUpdateImage}
            onUpdateObject={handleUpdateObject}
            onDeleteImage={handleDeleteImage}
            onDeleteObject={handleDeleteObject}
            onAddObject={handleAddObject}
          />
        </div>
      </div>
    </div>
  );
};

export default PuzzleTool;
