'use client';

const Toolbar = ({ tool, onToolChange, mode }) => {
  const tools = [
    {
      id: 'select',
      name: '选择',
      icon: '👆',
      description: '选择和移动对象',
    },
    {
      id: 'text',
      name: '文字',
      icon: '📝',
      description: '添加文字',
    },
    {
      id: 'arrow',
      name: '箭头',
      icon: '➡️',
      description: '添加箭头',
    },
    {
      id: 'rectangle',
      name: '方框',
      icon: '⬜',
      description: '添加矩形',
    },
    {
      id: 'circle',
      name: '圆圈',
      icon: '⭕',
      description: '添加圆形',
    },
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">工具栏</h3>
      
      <div className="grid grid-cols-2 gap-2">
        {tools.map((toolItem) => (
          <button
            key={toolItem.id}
            onClick={() => onToolChange(toolItem.id)}
            className={`
              p-3 rounded-lg border text-left transition-all duration-200
              ${tool === toolItem.id
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
            `}
            title={toolItem.description}
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg">{toolItem.icon}</span>
              <span className="text-sm font-medium">{toolItem.name}</span>
            </div>
          </button>
        ))}
      </div>

      {/* 模式说明 */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-2">
          当前模式：{mode === 'layout' ? '布局拼图' : '长图拼接'}
        </h4>
        <div className="text-xs text-gray-600 space-y-1">
          {mode === 'layout' ? (
            <>
              <div>• 可自由拖拽、缩放、旋转图片</div>
              <div>• 支持添加创意对象装饰</div>
              <div>• 点击"自动排列"快速布局</div>
            </>
          ) : (
            <>
              <div>• 图片按顺序拼接成长图</div>
              <div>• 支持水平或垂直拼接</div>
              <div>• 可添加文字和标注</div>
            </>
          )}
        </div>
      </div>

      {/* 操作提示 */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-700 mb-2">操作提示</h4>
        <div className="text-xs text-blue-600 space-y-1">
          <div>• 选择工具后在画布上点击添加</div>
          <div>• 双击对象可编辑属性</div>
          <div>• 按Delete键删除选中对象</div>
          <div>• 拖拽角落可调整大小</div>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;
