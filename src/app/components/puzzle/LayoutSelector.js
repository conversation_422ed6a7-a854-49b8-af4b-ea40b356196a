'use client';

import { useState } from 'react';

const LayoutSelector = ({ images, onApplyLayout, mode }) => {
  const [selectedLayout, setSelectedLayout] = useState(null);

  // 预设布局配置
  const layouts = {
    1: [
      {
        id: '1-center',
        name: '居中',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
            <div className="w-8 h-6 bg-blue-400 rounded"></div>
          </div>
        ),
        positions: [{ x: 0.5, y: 0.5, width: 0.6, height: 0.6 }]
      }
    ],
    2: [
      {
        id: '2-horizontal',
        name: '水平排列',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center gap-1">
            <div className="w-3 h-6 bg-blue-400 rounded"></div>
            <div className="w-3 h-6 bg-green-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.1, y: 0.3, width: 0.35, height: 0.4 },
          { x: 0.55, y: 0.3, width: 0.35, height: 0.4 }
        ]
      },
      {
        id: '2-vertical',
        name: '垂直排列',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex flex-col items-center justify-center gap-1">
            <div className="w-6 h-2.5 bg-blue-400 rounded"></div>
            <div className="w-6 h-2.5 bg-green-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.3, y: 0.1, width: 0.4, height: 0.35 },
          { x: 0.3, y: 0.55, width: 0.4, height: 0.35 }
        ]
      },
      {
        id: '2-overlap',
        name: '重叠布局',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-5 h-4 bg-blue-400 rounded absolute"></div>
            <div className="w-5 h-4 bg-green-400 rounded absolute translate-x-2 translate-y-1"></div>
          </div>
        ),
        positions: [
          { x: 0.2, y: 0.25, width: 0.5, height: 0.4 },
          { x: 0.3, y: 0.35, width: 0.5, height: 0.4 }
        ]
      }
    ],
    3: [
      {
        id: '3-horizontal',
        name: '水平三联',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center gap-0.5">
            <div className="w-2 h-5 bg-blue-400 rounded"></div>
            <div className="w-2 h-5 bg-green-400 rounded"></div>
            <div className="w-2 h-5 bg-red-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.05, y: 0.3, width: 0.25, height: 0.4 },
          { x: 0.375, y: 0.3, width: 0.25, height: 0.4 },
          { x: 0.7, y: 0.3, width: 0.25, height: 0.4 }
        ]
      },
      {
        id: '3-vertical',
        name: '垂直三联',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex flex-col items-center justify-center gap-0.5">
            <div className="w-5 h-1.5 bg-blue-400 rounded"></div>
            <div className="w-5 h-1.5 bg-green-400 rounded"></div>
            <div className="w-5 h-1.5 bg-red-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.3, y: 0.05, width: 0.4, height: 0.25 },
          { x: 0.3, y: 0.375, width: 0.4, height: 0.25 },
          { x: 0.3, y: 0.7, width: 0.4, height: 0.25 }
        ]
      },
      {
        id: '3-triangle',
        name: '三角布局',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-3 h-2 bg-blue-400 rounded absolute -translate-y-2"></div>
            <div className="w-3 h-2 bg-green-400 rounded absolute translate-y-1 -translate-x-2"></div>
            <div className="w-3 h-2 bg-red-400 rounded absolute translate-y-1 translate-x-2"></div>
          </div>
        ),
        positions: [
          { x: 0.35, y: 0.1, width: 0.3, height: 0.3 },
          { x: 0.1, y: 0.6, width: 0.3, height: 0.3 },
          { x: 0.6, y: 0.6, width: 0.3, height: 0.3 }
        ]
      },
      {
        id: '3-large-small',
        name: '主次布局',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-4 h-5 bg-blue-400 rounded absolute -translate-x-1"></div>
            <div className="w-2 h-2 bg-green-400 rounded absolute translate-x-2 -translate-y-1"></div>
            <div className="w-2 h-2 bg-red-400 rounded absolute translate-x-2 translate-y-1"></div>
          </div>
        ),
        positions: [
          { x: 0.1, y: 0.2, width: 0.5, height: 0.6 },
          { x: 0.65, y: 0.2, width: 0.25, height: 0.25 },
          { x: 0.65, y: 0.55, width: 0.25, height: 0.25 }
        ]
      }
    ],
    4: [
      {
        id: '4-grid',
        name: '四宫格',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded grid grid-cols-2 gap-0.5 p-1">
            <div className="bg-blue-400 rounded"></div>
            <div className="bg-green-400 rounded"></div>
            <div className="bg-red-400 rounded"></div>
            <div className="bg-yellow-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.1, y: 0.1, width: 0.35, height: 0.35 },
          { x: 0.55, y: 0.1, width: 0.35, height: 0.35 },
          { x: 0.1, y: 0.55, width: 0.35, height: 0.35 },
          { x: 0.55, y: 0.55, width: 0.35, height: 0.35 }
        ]
      },
      {
        id: '4-horizontal',
        name: '水平四联',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center gap-0.5">
            <div className="w-1.5 h-4 bg-blue-400 rounded"></div>
            <div className="w-1.5 h-4 bg-green-400 rounded"></div>
            <div className="w-1.5 h-4 bg-red-400 rounded"></div>
            <div className="w-1.5 h-4 bg-yellow-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.05, y: 0.3, width: 0.2, height: 0.4 },
          { x: 0.275, y: 0.3, width: 0.2, height: 0.4 },
          { x: 0.5, y: 0.3, width: 0.2, height: 0.4 },
          { x: 0.725, y: 0.3, width: 0.2, height: 0.4 }
        ]
      },
      {
        id: '4-large-three',
        name: '主图三小',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-4 h-5 bg-blue-400 rounded absolute -translate-x-1"></div>
            <div className="w-1.5 h-1.5 bg-green-400 rounded absolute translate-x-2 -translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-red-400 rounded absolute translate-x-2"></div>
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded absolute translate-x-2 translate-y-2"></div>
          </div>
        ),
        positions: [
          { x: 0.1, y: 0.2, width: 0.5, height: 0.6 },
          { x: 0.65, y: 0.1, width: 0.25, height: 0.2 },
          { x: 0.65, y: 0.4, width: 0.25, height: 0.2 },
          { x: 0.65, y: 0.7, width: 0.25, height: 0.2 }
        ]
      }
    ],
    5: [
      {
        id: '5-cross',
        name: '十字布局',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-2 h-2 bg-blue-400 rounded absolute"></div>
            <div className="w-1.5 h-1.5 bg-green-400 rounded absolute -translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-red-400 rounded absolute translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded absolute -translate-x-2"></div>
            <div className="w-1.5 h-1.5 bg-purple-400 rounded absolute translate-x-2"></div>
          </div>
        ),
        positions: [
          { x: 0.375, y: 0.375, width: 0.25, height: 0.25 },
          { x: 0.375, y: 0.1, width: 0.25, height: 0.2 },
          { x: 0.375, y: 0.7, width: 0.25, height: 0.2 },
          { x: 0.1, y: 0.375, width: 0.2, height: 0.25 },
          { x: 0.7, y: 0.375, width: 0.2, height: 0.25 }
        ]
      },
      {
        id: '5-large-four',
        name: '主图四角',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-3 h-3 bg-blue-400 rounded absolute"></div>
            <div className="w-1.5 h-1.5 bg-green-400 rounded absolute -translate-x-3 -translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-red-400 rounded absolute translate-x-3 -translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded absolute -translate-x-3 translate-y-2"></div>
            <div className="w-1.5 h-1.5 bg-purple-400 rounded absolute translate-x-3 translate-y-2"></div>
          </div>
        ),
        positions: [
          { x: 0.3, y: 0.3, width: 0.4, height: 0.4 },
          { x: 0.05, y: 0.05, width: 0.2, height: 0.2 },
          { x: 0.75, y: 0.05, width: 0.2, height: 0.2 },
          { x: 0.05, y: 0.75, width: 0.2, height: 0.2 },
          { x: 0.75, y: 0.75, width: 0.2, height: 0.2 }
        ]
      }
    ],
    6: [
      {
        id: '6-grid',
        name: '六宫格',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded grid grid-cols-3 gap-0.5 p-1">
            <div className="bg-blue-400 rounded"></div>
            <div className="bg-green-400 rounded"></div>
            <div className="bg-red-400 rounded"></div>
            <div className="bg-yellow-400 rounded"></div>
            <div className="bg-purple-400 rounded"></div>
            <div className="bg-pink-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.05, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.375, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.7, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.05, y: 0.55, width: 0.25, height: 0.35 },
          { x: 0.375, y: 0.55, width: 0.25, height: 0.35 },
          { x: 0.7, y: 0.55, width: 0.25, height: 0.35 }
        ]
      },
      {
        id: '6-two-rows',
        name: '双行布局',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex flex-col gap-0.5 p-1">
            <div className="flex gap-0.5 flex-1">
              <div className="bg-blue-400 rounded flex-1"></div>
              <div className="bg-green-400 rounded flex-1"></div>
              <div className="bg-red-400 rounded flex-1"></div>
            </div>
            <div className="flex gap-0.5 flex-1">
              <div className="bg-yellow-400 rounded flex-1"></div>
              <div className="bg-purple-400 rounded flex-1"></div>
              <div className="bg-pink-400 rounded flex-1"></div>
            </div>
          </div>
        ),
        positions: [
          { x: 0.05, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.375, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.7, y: 0.1, width: 0.25, height: 0.35 },
          { x: 0.05, y: 0.55, width: 0.25, height: 0.35 },
          { x: 0.375, y: 0.55, width: 0.25, height: 0.35 },
          { x: 0.7, y: 0.55, width: 0.25, height: 0.35 }
        ]
      }
    ],
    9: [
      {
        id: '9-grid',
        name: '九宫格',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded grid grid-cols-3 gap-0.5 p-1">
            <div className="bg-blue-400 rounded"></div>
            <div className="bg-green-400 rounded"></div>
            <div className="bg-red-400 rounded"></div>
            <div className="bg-yellow-400 rounded"></div>
            <div className="bg-purple-400 rounded"></div>
            <div className="bg-pink-400 rounded"></div>
            <div className="bg-indigo-400 rounded"></div>
            <div className="bg-orange-400 rounded"></div>
            <div className="bg-teal-400 rounded"></div>
          </div>
        ),
        positions: [
          { x: 0.05, y: 0.05, width: 0.25, height: 0.25 },
          { x: 0.375, y: 0.05, width: 0.25, height: 0.25 },
          { x: 0.7, y: 0.05, width: 0.25, height: 0.25 },
          { x: 0.05, y: 0.375, width: 0.25, height: 0.25 },
          { x: 0.375, y: 0.375, width: 0.25, height: 0.25 },
          { x: 0.7, y: 0.375, width: 0.25, height: 0.25 },
          { x: 0.05, y: 0.7, width: 0.25, height: 0.25 },
          { x: 0.375, y: 0.7, width: 0.25, height: 0.25 },
          { x: 0.7, y: 0.7, width: 0.25, height: 0.25 }
        ]
      },
      {
        id: '9-center-focus',
        name: '中心焦点',
        preview: (
          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center relative">
            <div className="w-4 h-4 bg-blue-400 rounded absolute"></div>
            <div className="w-2 h-2 bg-green-400 rounded absolute -translate-x-3 -translate-y-3"></div>
            <div className="w-2 h-2 bg-red-400 rounded absolute translate-x-0 -translate-y-3"></div>
            <div className="w-2 h-2 bg-yellow-400 rounded absolute translate-x-3 -translate-y-3"></div>
            <div className="w-2 h-2 bg-purple-400 rounded absolute -translate-x-3 translate-y-0"></div>
            <div className="w-2 h-2 bg-pink-400 rounded absolute translate-x-3 translate-y-0"></div>
            <div className="w-2 h-2 bg-indigo-400 rounded absolute -translate-x-3 translate-y-3"></div>
            <div className="w-2 h-2 bg-orange-400 rounded absolute translate-x-0 translate-y-3"></div>
            <div className="w-2 h-2 bg-teal-400 rounded absolute translate-x-3 translate-y-3"></div>
          </div>
        ),
        positions: [
          { x: 0.35, y: 0.35, width: 0.3, height: 0.3 },
          { x: 0.05, y: 0.05, width: 0.2, height: 0.2 },
          { x: 0.4, y: 0.05, width: 0.2, height: 0.2 },
          { x: 0.75, y: 0.05, width: 0.2, height: 0.2 },
          { x: 0.05, y: 0.4, width: 0.2, height: 0.2 },
          { x: 0.75, y: 0.4, width: 0.2, height: 0.2 },
          { x: 0.05, y: 0.75, width: 0.2, height: 0.2 },
          { x: 0.4, y: 0.75, width: 0.2, height: 0.2 },
          { x: 0.75, y: 0.75, width: 0.2, height: 0.2 }
        ]
      }
    ]
  };

  const handleLayoutSelect = (layout) => {
    setSelectedLayout(layout.id);
    onApplyLayout(layout);
  };

  const imageCount = images.length;
  const availableLayouts = layouts[imageCount] || [];

  if (mode !== 'layout' || imageCount === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">
        布局选择 ({imageCount}张图片)
      </h3>

      {availableLayouts.length > 0 ? (
        <div className="grid grid-cols-2 gap-3">
          {availableLayouts.map((layout) => (
            <button
              key={layout.id}
              onClick={() => handleLayoutSelect(layout)}
              className={`
                p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-md group
                ${selectedLayout === layout.id
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                }
              `}
            >
              <div className="w-full h-16 mb-2 relative overflow-hidden rounded">
                {layout.preview}
                {selectedLayout === layout.id && (
                  <div className="absolute inset-0 bg-blue-500 bg-opacity-10 flex items-center justify-center">
                    <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
              <div className={`text-xs font-medium transition-colors ${
                selectedLayout === layout.id ? 'text-blue-700' : 'text-gray-700 group-hover:text-gray-900'
              }`}>
                {layout.name}
              </div>
            </button>
          ))}
        </div>
      ) : (
        <div className="text-sm text-gray-500 text-center py-4">
          暂无适合 {imageCount} 张图片的预设布局
        </div>
      )}

      <div className="text-xs text-gray-500 space-y-1">
        <div>💡 提示：</div>
        <div>• 选择布局后图片会自动排列</div>
        <div>• 可以继续手动调整位置和大小</div>
        <div>• 支持1-9张图片的多种布局</div>
        <div>• 点击"重置布局"恢复原始状态</div>
      </div>
    </div>
  );
};

export default LayoutSelector;
