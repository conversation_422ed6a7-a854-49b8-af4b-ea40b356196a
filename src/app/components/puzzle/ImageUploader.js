'use client';

import { useState, useCallback } from 'react';

const ImageUploader = ({ onImagesUpload }) => {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = useCallback((files) => {
    if (!files || files.length === 0) return;

    setUploading(true);
    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    
    if (imageFiles.length === 0) {
      alert('请选择图片文件');
      setUploading(false);
      return;
    }

    const loadPromises = imageFiles.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            resolve({
              src: e.target.result,
              width: img.width,
              height: img.height,
              name: file.name,
            });
          };
          img.src = e.target.result;
        };
        reader.readAsDataURL(file);
      });
    });

    Promise.all(loadPromises).then(images => {
      onImagesUpload(images);
      setUploading(false);
    });
  }, [onImagesUpload]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleInputChange = useCallback((e) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">图片上传</h3>
      
      {/* 拖拽上传区域 */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
          ${dragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${uploading ? 'opacity-50 pointer-events-none' : ''}
        `}
      >
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={handleInputChange}
          className="hidden"
          id="image-upload"
          disabled={uploading}
        />
        <label htmlFor="image-upload" className="cursor-pointer">
          <div className="space-y-2">
            <div className="text-gray-400">
              <svg className="mx-auto h-12 w-12" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </div>
            <div className="text-sm text-gray-600">
              {uploading ? (
                <span>正在上传...</span>
              ) : (
                <>
                  <span className="font-medium text-blue-600">点击上传</span>
                  <span> 或拖拽图片到此处</span>
                </>
              )}
            </div>
            <div className="text-xs text-gray-500">
              支持 PNG, JPG, GIF 等格式，可多选
            </div>
          </div>
        </label>
      </div>

      {/* 快速操作提示 */}
      <div className="text-xs text-gray-500 space-y-1">
        <div>💡 提示：</div>
        <div>• 支持同时上传多张图片</div>
        <div>• 上传后可在画布中自由编辑</div>
        <div>• 支持拖拽、缩放、旋转操作</div>
      </div>
    </div>
  );
};

export default ImageUploader;
