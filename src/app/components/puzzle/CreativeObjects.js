'use client';

import { useState } from 'react';

const CreativeObjects = ({ 
  tool, 
  onAddObject, 
  selectedObject, 
  onUpdateObject, 
  onDeleteObject 
}) => {
  const [textInput, setTextInput] = useState('');
  const [fontSize, setFontSize] = useState(16);
  const [color, setColor] = useState('#000000');
  const [strokeColor, setStrokeColor] = useState('#000000');
  const [fillColor, setFillColor] = useState('#ffffff');
  const [strokeWidth, setStrokeWidth] = useState(2);

  const handleAddText = () => {
    if (!textInput.trim()) return;
    
    onAddObject({
      type: 'text',
      text: textInput,
      x: 100,
      y: 100,
      fontSize,
      color,
      fontFamily: 'Arial, sans-serif',
    });
    
    setTextInput('');
  };

  const handleAddArrow = () => {
    onAddObject({
      type: 'arrow',
      x: 100,
      y: 100,
      width: 100,
      height: 20,
      color: strokeColor,
      strokeWidth,
    });
  };

  const handleAddRectangle = () => {
    onAddObject({
      type: 'rectangle',
      x: 100,
      y: 100,
      width: 100,
      height: 60,
      strokeColor,
      fillColor,
      strokeWidth,
    });
  };

  const handleAddCircle = () => {
    onAddObject({
      type: 'circle',
      x: 100,
      y: 100,
      radius: 50,
      strokeColor,
      fillColor,
      strokeWidth,
    });
  };

  const handleUpdateSelectedObject = (property, value) => {
    if (selectedObject) {
      onUpdateObject(selectedObject.id, { [property]: value });
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800">创意对象</h3>

      {/* 文字工具 */}
      {tool === 'text' && (
        <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700">添加文字</h4>
          <div className="space-y-2">
            <input
              type="text"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="输入文字内容"
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              onKeyPress={(e) => e.key === 'Enter' && handleAddText()}
            />
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">字号</label>
                <input
                  type="number"
                  value={fontSize}
                  onChange={(e) => setFontSize(Number(e.target.value))}
                  min="8"
                  max="72"
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">颜色</label>
                <input
                  type="color"
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
            </div>
            <button
              onClick={handleAddText}
              disabled={!textInput.trim()}
              className="w-full px-3 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              添加文字
            </button>
          </div>
        </div>
      )}

      {/* 箭头工具 */}
      {tool === 'arrow' && (
        <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700">添加箭头</h4>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">颜色</label>
                <input
                  type="color"
                  value={strokeColor}
                  onChange={(e) => setStrokeColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">粗细</label>
                <input
                  type="number"
                  value={strokeWidth}
                  onChange={(e) => setStrokeWidth(Number(e.target.value))}
                  min="1"
                  max="10"
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
            </div>
            <button
              onClick={handleAddArrow}
              className="w-full px-3 py-2 bg-green-500 text-white rounded-md text-sm hover:bg-green-600"
            >
              添加箭头
            </button>
          </div>
        </div>
      )}

      {/* 矩形工具 */}
      {tool === 'rectangle' && (
        <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700">添加矩形</h4>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">边框</label>
                <input
                  type="color"
                  value={strokeColor}
                  onChange={(e) => setStrokeColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">填充</label>
                <input
                  type="color"
                  value={fillColor}
                  onChange={(e) => setFillColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">边框粗细</label>
              <input
                type="number"
                value={strokeWidth}
                onChange={(e) => setStrokeWidth(Number(e.target.value))}
                min="0"
                max="10"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <button
              onClick={handleAddRectangle}
              className="w-full px-3 py-2 bg-orange-500 text-white rounded-md text-sm hover:bg-orange-600"
            >
              添加矩形
            </button>
          </div>
        </div>
      )}

      {/* 圆形工具 */}
      {tool === 'circle' && (
        <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700">添加圆形</h4>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">边框</label>
                <input
                  type="color"
                  value={strokeColor}
                  onChange={(e) => setStrokeColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
              <div className="flex-1">
                <label className="block text-xs text-gray-600 mb-1">填充</label>
                <input
                  type="color"
                  value={fillColor}
                  onChange={(e) => setFillColor(e.target.value)}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">边框粗细</label>
              <input
                type="number"
                value={strokeWidth}
                onChange={(e) => setStrokeWidth(Number(e.target.value))}
                min="0"
                max="10"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
            <button
              onClick={handleAddCircle}
              className="w-full px-3 py-2 bg-purple-500 text-white rounded-md text-sm hover:bg-purple-600"
            >
              添加圆形
            </button>
          </div>
        </div>
      )}

      {/* 选中对象的属性编辑 */}
      {selectedObject && tool === 'select' && (
        <div className="space-y-3 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-blue-700">
              编辑 {selectedObject.type === 'text' ? '文字' : 
                    selectedObject.type === 'arrow' ? '箭头' :
                    selectedObject.type === 'rectangle' ? '矩形' : '圆形'}
            </h4>
            <button
              onClick={() => onDeleteObject(selectedObject.id)}
              className="text-red-500 hover:text-red-700 text-sm"
            >
              删除
            </button>
          </div>

          {selectedObject.type === 'text' && (
            <div className="space-y-2">
              <input
                type="text"
                value={selectedObject.text || ''}
                onChange={(e) => handleUpdateSelectedObject('text', e.target.value)}
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
              <div className="flex space-x-2">
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">字号</label>
                  <input
                    type="number"
                    value={selectedObject.fontSize || 16}
                    onChange={(e) => handleUpdateSelectedObject('fontSize', Number(e.target.value))}
                    min="8"
                    max="72"
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">颜色</label>
                  <input
                    type="color"
                    value={selectedObject.color || '#000000'}
                    onChange={(e) => handleUpdateSelectedObject('color', e.target.value)}
                    className="w-full h-8 border border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
          )}

          {(selectedObject.type === 'rectangle' || selectedObject.type === 'circle') && (
            <div className="space-y-2">
              <div className="flex space-x-2">
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">边框</label>
                  <input
                    type="color"
                    value={selectedObject.strokeColor || '#000000'}
                    onChange={(e) => handleUpdateSelectedObject('strokeColor', e.target.value)}
                    className="w-full h-8 border border-gray-300 rounded"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-xs text-gray-600 mb-1">填充</label>
                  <input
                    type="color"
                    value={selectedObject.fillColor || '#ffffff'}
                    onChange={(e) => handleUpdateSelectedObject('fillColor', e.target.value)}
                    className="w-full h-8 border border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CreativeObjects;
