'use client';

import { forwardRef, useRef, useEffect, useState, useCallback } from 'react';

const Canvas = forwardRef(({
  mode,
  images,
  canvasObjects,
  selectedImage,
  selectedObject,
  tool,
  onSelectImage,
  onSelectObject,
  onUpdateImage,
  onUpdateObject,
  onDeleteImage,
  onDeleteObject,
  onAddObject,
}, ref) => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [dragTarget, setDragTarget] = useState(null);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null);

  // 获取鼠标在画布中的相对位置
  const getCanvasPosition = useCallback((e) => {
    const rect = canvasRef.current.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }, []);

  // 检查点是否在对象内
  const isPointInObject = useCallback((point, obj) => {
    if (obj.type === 'image') {
      return point.x >= obj.x &&
             point.x <= obj.x + obj.width &&
             point.y >= obj.y &&
             point.y <= obj.y + obj.height;
    } else if (obj.type === 'text') {
      // 简化的文字边界检测
      const textWidth = (obj.text?.length || 0) * (obj.fontSize || 16) * 0.6;
      const textHeight = obj.fontSize || 16;
      return point.x >= obj.x &&
             point.x <= obj.x + textWidth &&
             point.y >= obj.y - textHeight &&
             point.y <= obj.y;
    } else if (obj.type === 'rectangle') {
      return point.x >= obj.x &&
             point.x <= obj.x + obj.width &&
             point.y >= obj.y &&
             point.y <= obj.y + obj.height;
    } else if (obj.type === 'circle') {
      const centerX = obj.x + obj.radius;
      const centerY = obj.y + obj.radius;
      const distance = Math.sqrt(
        Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2)
      );
      return distance <= obj.radius;
    } else if (obj.type === 'arrow') {
      // 简化的箭头检测
      return point.x >= obj.x &&
             point.x <= obj.x + obj.width &&
             point.y >= obj.y &&
             point.y <= obj.y + obj.height;
    }
    return false;
  }, []);

  // 获取调整大小的手柄
  const getResizeHandle = useCallback((point, obj) => {
    if (!obj || obj.type === 'text' || obj.type === 'arrow') return null;

    const handleSize = 8;
    const handles = [];

    if (obj.type === 'image' || obj.type === 'rectangle') {
      handles.push(
        { name: 'nw', x: obj.x - handleSize/2, y: obj.y - handleSize/2 },
        { name: 'ne', x: obj.x + obj.width - handleSize/2, y: obj.y - handleSize/2 },
        { name: 'sw', x: obj.x - handleSize/2, y: obj.y + obj.height - handleSize/2 },
        { name: 'se', x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height - handleSize/2 }
      );
    } else if (obj.type === 'circle') {
      const radius = obj.radius;
      handles.push(
        { name: 'n', x: obj.x + radius - handleSize/2, y: obj.y - handleSize/2 },
        { name: 's', x: obj.x + radius - handleSize/2, y: obj.y + radius * 2 - handleSize/2 },
        { name: 'e', x: obj.x + radius * 2 - handleSize/2, y: obj.y + radius - handleSize/2 },
        { name: 'w', x: obj.x - handleSize/2, y: obj.y + radius - handleSize/2 }
      );
    }

    for (const handle of handles) {
      if (point.x >= handle.x && point.x <= handle.x + handleSize &&
          point.y >= handle.y && point.y <= handle.y + handleSize) {
        return handle.name;
      }
    }
    return null;
  }, []);

  // 鼠标按下事件
  const handleMouseDown = useCallback((e) => {
    const point = getCanvasPosition(e);

    // 检查是否点击了调整大小的手柄
    const selectedObj = selectedImage || selectedObject;
    if (selectedObj && tool === 'select') {
      const handle = getResizeHandle(point, selectedObj);
      if (handle) {
        setIsResizing(true);
        setResizeHandle(handle);
        setDragStart(point);
        return;
      }
    }

    // 检查是否点击了现有对象
    let clickedObject = null;

    // 先检查创意对象（在上层）
    for (let i = canvasObjects.length - 1; i >= 0; i--) {
      if (isPointInObject(point, canvasObjects[i])) {
        clickedObject = { type: 'object', data: canvasObjects[i] };
        break;
      }
    }

    // 再检查图片
    if (!clickedObject) {
      for (let i = images.length - 1; i >= 0; i--) {
        if (isPointInObject(point, images[i])) {
          clickedObject = { type: 'image', data: images[i] };
          break;
        }
      }
    }

    if (clickedObject) {
      // 选中对象
      if (clickedObject.type === 'image') {
        onSelectImage(clickedObject.data);
        onSelectObject(null);
      } else {
        onSelectObject(clickedObject.data);
        onSelectImage(null);
      }

      if (tool === 'select') {
        setIsDragging(true);
        setDragTarget(clickedObject);
        setDragStart(point);
      }
    } else {
      // 点击空白区域
      onSelectImage(null);
      onSelectObject(null);

      // 根据当前工具添加新对象
      if (tool !== 'select') {
        if (tool === 'text') {
          onAddObject({
            type: 'text',
            text: '双击编辑文字',
            x: point.x,
            y: point.y,
            fontSize: 16,
            color: '#000000',
            fontFamily: 'Arial, sans-serif',
          });
        } else if (tool === 'arrow') {
          onAddObject({
            type: 'arrow',
            x: point.x,
            y: point.y,
            width: 100,
            height: 20,
            color: '#000000',
            strokeWidth: 2,
          });
        } else if (tool === 'rectangle') {
          onAddObject({
            type: 'rectangle',
            x: point.x,
            y: point.y,
            width: 100,
            height: 60,
            strokeColor: '#000000',
            fillColor: '#ffffff',
            strokeWidth: 2,
          });
        } else if (tool === 'circle') {
          onAddObject({
            type: 'circle',
            x: point.x,
            y: point.y,
            radius: 50,
            strokeColor: '#000000',
            fillColor: '#ffffff',
            strokeWidth: 2,
          });
        }
      }
    }
  }, [
    getCanvasPosition, selectedImage, selectedObject, tool, getResizeHandle,
    canvasObjects, images, isPointInObject, onSelectImage, onSelectObject, onAddObject
  ]);

  // 鼠标移动事件
  const handleMouseMove = useCallback((e) => {
    const point = getCanvasPosition(e);

    if (isResizing && resizeHandle && (selectedImage || selectedObject)) {
      const obj = selectedImage || selectedObject;
      const deltaX = point.x - dragStart.x;
      const deltaY = point.y - dragStart.y;

      let updates = {};

      if (obj.type === 'image' || obj.type === 'rectangle') {
        switch (resizeHandle) {
          case 'se':
            updates = {
              width: Math.max(20, obj.width + deltaX),
              height: Math.max(20, obj.height + deltaY)
            };
            break;
          case 'sw':
            updates = {
              x: obj.x + deltaX,
              width: Math.max(20, obj.width - deltaX),
              height: Math.max(20, obj.height + deltaY)
            };
            break;
          case 'ne':
            updates = {
              y: obj.y + deltaY,
              width: Math.max(20, obj.width + deltaX),
              height: Math.max(20, obj.height - deltaY)
            };
            break;
          case 'nw':
            updates = {
              x: obj.x + deltaX,
              y: obj.y + deltaY,
              width: Math.max(20, obj.width - deltaX),
              height: Math.max(20, obj.height - deltaY)
            };
            break;
        }
      } else if (obj.type === 'circle') {
        const centerX = obj.x + obj.radius;
        const centerY = obj.y + obj.radius;
        const newRadius = Math.max(10, Math.sqrt(
          Math.pow(point.x - centerX, 2) + Math.pow(point.y - centerY, 2)
        ));
        updates = {
          radius: newRadius,
          x: centerX - newRadius,
          y: centerY - newRadius
        };
      }

      if (selectedImage) {
        onUpdateImage(selectedImage.id, updates);
      } else {
        onUpdateObject(selectedObject.id, updates);
      }

      setDragStart(point);
    } else if (isDragging && dragTarget) {
      const deltaX = point.x - dragStart.x;
      const deltaY = point.y - dragStart.y;

      const updates = {
        x: dragTarget.data.x + deltaX,
        y: dragTarget.data.y + deltaY,
      };

      if (dragTarget.type === 'image') {
        onUpdateImage(dragTarget.data.id, updates);
      } else {
        onUpdateObject(dragTarget.data.id, updates);
      }

      setDragStart(point);
    }
  }, [
    getCanvasPosition, isResizing, resizeHandle, selectedImage, selectedObject,
    dragStart, isDragging, dragTarget, onUpdateImage, onUpdateObject
  ]);

  // 鼠标释放事件
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
    setDragTarget(null);
    setResizeHandle(null);
  }, []);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Delete' || e.key === 'Backspace') {
        if (selectedImage) {
          onDeleteImage(selectedImage.id);
        } else if (selectedObject) {
          onDeleteObject(selectedObject.id);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, selectedObject, onDeleteImage, onDeleteObject]);

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-auto bg-white relative"
      style={{ minHeight: '600px' }}
    >
      <div
        ref={canvasRef}
        className="relative bg-gray-100 border-2 border-dashed border-gray-300 cursor-crosshair"
        style={{
          width: '2000px',
          height: '1500px',
          margin: '20px',
          backgroundImage: `
            radial-gradient(circle, #e5e7eb 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* 渲染图片 */}
        {images.map((image) => (
          <div
            key={image.id}
            className={`absolute border-2 ${
              selectedImage?.id === image.id
                ? 'border-blue-500 shadow-lg'
                : 'border-transparent hover:border-gray-400'
            }`}
            style={{
              left: image.x,
              top: image.y,
              width: image.width,
              height: image.height,
              transform: `rotate(${image.rotation || 0}deg) scale(${image.scale || 1})`,
              transformOrigin: 'center',
              cursor: tool === 'select' ? 'move' : 'crosshair',
            }}
          >
            <img
              src={image.src}
              alt=""
              className="w-full h-full object-cover"
              draggable={false}
            />
          </div>
        ))}

        {/* 渲染创意对象 */}
        {canvasObjects.map((obj) => (
          <div key={obj.id}>
            {obj.type === 'text' && (
              <div
                className={`absolute select-none ${
                  selectedObject?.id === obj.id
                    ? 'ring-2 ring-blue-500'
                    : ''
                }`}
                style={{
                  left: obj.x,
                  top: obj.y - (obj.fontSize || 16),
                  fontSize: obj.fontSize || 16,
                  color: obj.color || '#000000',
                  fontFamily: obj.fontFamily || 'Arial, sans-serif',
                  cursor: tool === 'select' ? 'move' : 'crosshair',
                  whiteSpace: 'nowrap',
                }}
              >
                {obj.text || '文字'}
              </div>
            )}

            {obj.type === 'rectangle' && (
              <div
                className={`absolute ${
                  selectedObject?.id === obj.id
                    ? 'ring-2 ring-blue-500'
                    : ''
                }`}
                style={{
                  left: obj.x,
                  top: obj.y,
                  width: obj.width,
                  height: obj.height,
                  border: `${obj.strokeWidth || 2}px solid ${obj.strokeColor || '#000000'}`,
                  backgroundColor: obj.fillColor || '#ffffff',
                  cursor: tool === 'select' ? 'move' : 'crosshair',
                }}
              />
            )}

            {obj.type === 'circle' && (
              <div
                className={`absolute rounded-full ${
                  selectedObject?.id === obj.id
                    ? 'ring-2 ring-blue-500'
                    : ''
                }`}
                style={{
                  left: obj.x,
                  top: obj.y,
                  width: obj.radius * 2,
                  height: obj.radius * 2,
                  border: `${obj.strokeWidth || 2}px solid ${obj.strokeColor || '#000000'}`,
                  backgroundColor: obj.fillColor || '#ffffff',
                  cursor: tool === 'select' ? 'move' : 'crosshair',
                }}
              />
            )}

            {obj.type === 'arrow' && (
              <div
                className={`absolute ${
                  selectedObject?.id === obj.id
                    ? 'ring-2 ring-blue-500'
                    : ''
                }`}
                style={{
                  left: obj.x,
                  top: obj.y,
                  width: obj.width,
                  height: obj.height,
                  cursor: tool === 'select' ? 'move' : 'crosshair',
                }}
              >
                <svg width="100%" height="100%" viewBox="0 0 100 20">
                  <defs>
                    <marker
                      id={`arrowhead-${obj.id}`}
                      markerWidth="10"
                      markerHeight="7"
                      refX="9"
                      refY="3.5"
                      orient="auto"
                    >
                      <polygon
                        points="0 0, 10 3.5, 0 7"
                        fill={obj.color || '#000000'}
                      />
                    </marker>
                  </defs>
                  <line
                    x1="0"
                    y1="10"
                    x2="90"
                    y2="10"
                    stroke={obj.color || '#000000'}
                    strokeWidth={obj.strokeWidth || 2}
                    markerEnd={`url(#arrowhead-${obj.id})`}
                  />
                </svg>
              </div>
            )}
          </div>
        ))}

        {/* 渲染调整大小的手柄 */}
        {(selectedImage || selectedObject) && tool === 'select' && (
          <>
            {(() => {
              const obj = selectedImage || selectedObject;
              if (obj.type === 'text' || obj.type === 'arrow') return null;

              const handles = [];
              const handleSize = 8;

              if (obj.type === 'image' || obj.type === 'rectangle') {
                handles.push(
                  { name: 'nw', x: obj.x - handleSize/2, y: obj.y - handleSize/2 },
                  { name: 'ne', x: obj.x + obj.width - handleSize/2, y: obj.y - handleSize/2 },
                  { name: 'sw', x: obj.x - handleSize/2, y: obj.y + obj.height - handleSize/2 },
                  { name: 'se', x: obj.x + obj.width - handleSize/2, y: obj.y + obj.height - handleSize/2 }
                );
              } else if (obj.type === 'circle') {
                const radius = obj.radius;
                handles.push(
                  { name: 'n', x: obj.x + radius - handleSize/2, y: obj.y - handleSize/2 },
                  { name: 's', x: obj.x + radius - handleSize/2, y: obj.y + radius * 2 - handleSize/2 },
                  { name: 'e', x: obj.x + radius * 2 - handleSize/2, y: obj.y + radius - handleSize/2 },
                  { name: 'w', x: obj.x - handleSize/2, y: obj.y + radius - handleSize/2 }
                );
              }

              return handles.map((handle) => (
                <div
                  key={handle.name}
                  className="absolute bg-blue-500 border border-white cursor-pointer"
                  style={{
                    left: handle.x,
                    top: handle.y,
                    width: handleSize,
                    height: handleSize,
                    cursor: handle.name.includes('n') || handle.name.includes('s')
                      ? 'ns-resize'
                      : handle.name.includes('e') || handle.name.includes('w')
                      ? 'ew-resize'
                      : handle.name === 'nw' || handle.name === 'se'
                      ? 'nw-resize'
                      : 'ne-resize'
                  }}
                />
              ));
            })()}
          </>
        )}
      </div>
    </div>
  );
});

Canvas.displayName = 'Canvas';

export default Canvas;
