
import Image from "next/image";

export default function Tools() {
  return (
    <div className="flex flex-col items-center min-h-screen pt-20 relative bg-gray-50">
      <Image
        src="/homebrew.svg"
        alt="UECC"
        width={0}
        height={0}
        priority={true}
        style={{ width: "auto", height: "130px" }}
      />

      <h1 className="text-4xl font-bold mt-4">UECC</h1>
      <p className="text-lg mt-4">
        工具集，资源库
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10 w-full max-w-6xl px-4">
        <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
          <h2 className="text-xl font-semibold mb-2">在线文本</h2>
          <p className="text-gray-600 mb-4">实时共享和演示</p>
          <a href="/text" className="text-blue-500 hover:underline">去使用</a>
        </div>
        <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
          <h2 className="text-xl font-semibold mb-2">在线拼图工具</h2>
          <p className="text-gray-600 mb-4">布局拼图和长图拼接，支持创意对象</p>
          <a href="/puzzle" className="text-blue-500 hover:underline">去使用</a>
        </div>
        <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
          <h2 className="text-xl font-semibold mb-2">局域网文件传输</h2>
          <p className="text-gray-600 mb-4">无需互联网，直接传输文件</p>
          <a href="https://pairdrop.net/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">去使用</a>
        </div>
        {/* <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
          <h2 className="text-xl font-semibold mb-2">在线图片</h2>
          <p className="text-gray-600 mb-4">用于共享和演示</p>
          <a href="/image" className="text-blue-500 hover:underline">去使用</a>
        </div> */}
      </div>

      <footer className="w-full absolute bottom-0 left-0 py-4 bg-gray-100 text-center text-xs text-gray-500">
        <a
          href="https://beian.miit.gov.cn/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
        >
          辽ICP备18016669号-3
        </a>
        <span className="mx-2">|</span>
        <span>© 2025 UECC.CC 版权所有</span>
      </footer>
    </div>
  );
}
